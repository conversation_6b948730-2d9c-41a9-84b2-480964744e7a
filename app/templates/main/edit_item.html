{% extends "base.html" %}
{% import 'bootstrap_wtf.html' as wtf %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h2 class="card-title mb-0">编辑物品: {{ item.name }}</h2>
                </div>
                <div class="card-body">
                    <form method="POST" action="" enctype="multipart/form-data" novalidate>
                        {{ form.hidden_tag() }}
                        {{ wtf.render_field(form.name, class='form-control', placeholder='例如：九成新自行车') }}
                        {{ wtf.render_field(form.description, class='form-control', rows=5, placeholder='详细描述一下你的宝贝吧...') }}
                        {{ wtf.render_field(form.price, class='form-control', placeholder='0.00') }}
                        {{ wtf.render_field(form.category, class='form-select') }}
                        
                        <div class="mb-3">
                            <label class="form-label">当前图片</label><br>
                            {% if item.image_file and item.image_file != 'default.jpg' %}
                                <img src="{{ url_for('static', filename='uploads/' + item.image_file) }}" alt="Current Image" style="max-width: 200px; max-height: 200px; margin-bottom: 10px;">
                            {% else %}
                                <p>暂无图片</p>
                            {% endif %}
                        </div>
                        {{ wtf.render_field(form.image, class='form-control', label_text='更换图片 (可选)') }}
                        
                        <div class="d-grid gap-2">
                            {{ wtf.render_field(form.submit, class='btn btn-primary btn-lg', button_map={'submit':'warning'}, button_text='更新物品') }}
                        </div>
                    </form>
                </div>
                <div class="card-footer text-muted">
                    <a href="{{ url_for('main.item_detail', item_id=item.id) }}" class="btn btn-outline-secondary btn-sm">取消编辑</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}