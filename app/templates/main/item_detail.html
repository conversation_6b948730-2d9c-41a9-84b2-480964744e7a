{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title mb-0">{{ item.name }}</h2>
                </div>
                <div class="row g-0">
                    <div class="col-md-6">
                        {% if item.image_file and item.image_file != 'default.jpg' %}
                            <img src="{{ url_for('static', filename='uploads/' + item.image_file) }}" class="img-fluid rounded-start" alt="{{ item.name }}" style="width: 100%; height: auto; max-height: 400px; object-fit: cover;">
                        {% else %}
                            <img src="https://via.placeholder.com/400x300.png?text=No+Image+Available" class="img-fluid rounded-start" alt="{{ item.name }}" style="width: 100%; height: auto;">
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <div class="card-body">
                            <p class="card-text"><strong>价格:</strong> <span class="text-danger fs-4">¥{{ "%.2f"|format(item.price) }}</span></p>
                            <p class="card-text"><strong>描述:</strong> {{ item.description if item.description else '暂无描述' }}</p>
                            <p class="card-text"><strong>发布者:</strong> <a href="{{ url_for('main.user', username=item.seller.username) }}">{{ item.seller.username }}</a></p>
                            <p class="card-text"><strong>发布日期:</strong> {{ moment(item.date_posted).format('LLLL') if moment else item.date_posted.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                            {% if item.category %}
                            <p class="card-text"><strong>分类:</strong> {{ item.category.name }}</p>
                            {% endif %}
                            
                            {% if item.is_sold %}
                                <p class="text-success fw-bold">此物品已售出</p>
                            {% endif %}

                            {% if current_user.is_authenticated %}
                                {% if current_user == item.seller and not item.is_sold %}
                                    <a href="{{ url_for('main.edit_item', item_id=item.id) }}" class="btn btn-secondary btn-sm me-2">编辑物品</a>
                                    <form action="{{ url_for('main.delete_item', item_id=item.id) }}" method="POST" style="display:inline-block;" onsubmit="return confirm('确定要删除此物品吗？');">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() if csrf_token else '' }}"/>
                                        <button type="submit" class="btn btn-danger btn-sm me-2">删除物品</button>
                                    </form>
                                    <form action="{{ url_for('main.mark_sold', item_id=item.id) }}" method="POST" style="display:inline-block;">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() if csrf_token else '' }}"/>
                                        <button type="submit" class="btn btn-success btn-sm">标记为已售</button>
                                    </form>
                                {% elif current_user != item.seller and not item.is_sold %}
                                    <!-- Placeholder for 'Contact Seller' or 'Buy Now' button -->
                                    <a href="#" class="btn btn-info">联系卖家 (功能待实现)</a>
                                {% endif %}
                            {% else %}
                                <p><a href="{{ url_for('auth.login', next=request.url) }}">登录</a>后可联系卖家。</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-footer text-muted">
                    <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary btn-sm">&laquo; 返回列表</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}