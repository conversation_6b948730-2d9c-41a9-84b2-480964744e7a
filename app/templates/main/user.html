{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    <!-- Placeholder for profile picture -->
                    <img src="https://via.placeholder.com/150/007bff/FFFFFF?Text={{user.username[0]|upper}}" 
                         class="rounded-circle img-thumbnail mb-3" 
                         alt="{{ user.username }}" 
                         style="width: 150px; height: 150px; object-fit: cover;">
                    <h4 class="card-title">{{ user.username }}</h4>
                    <p class="text-muted">{{ user.email }}</p>
                    {% if user.about_me %}<p class="card-text"><em>{{ user.about_me }}</em></p>{% endif %}
                    {% if user.last_seen %}
                        <p class="card-text"><small class="text-muted">最后上线: {{ moment(user.last_seen).calendar() if moment else user.last_seen.strftime('%Y-%m-%d %H:%M') }}</small></p>
                    {% endif %}
                    {% if user == current_user %}
                        <a href="{{ url_for('main.edit_profile') }}" class="btn btn-outline-primary btn-sm">编辑个人资料</a>
                    {% else %}
                        <!-- Placeholder for follow/message buttons -->
                        <a href="#" class="btn btn-outline-success btn-sm">关注 (功能待实现)</a>
                        <a href="#" class="btn btn-outline-info btn-sm mt-2">发送私信 (功能待实现)</a>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-9">
            <h2 class="mb-4">{{ user.username }} 发布的物品 ({{ items.total if items else 0 }})</h2>
            {% if items and items.items %}
                <div class="row">
                    {% for item in items.items %}
                        <div class="col-md-6 col-lg-4">
                            <div class="card item-card shadow-sm">
                                {% if item.image_file and item.image_file != 'default.jpg' %}
                                    <img src="{{ url_for('static', filename='uploads/' + item.image_file) }}" class="card-img-top" alt="{{ item.name }}">
                                {% else %}
                                    <img src="https://via.placeholder.com/300x200.png?text=No+Image" class="card-img-top" alt="{{ item.name }}">
                                {% endif %}
                                <div class="card-body">
                                    <h5 class="card-title"><a href="{{ url_for('main.item_detail', item_id=item.id) }}">{{ item.name }}</a></h5>
                                    <p class="card-text text-muted">价格: ¥{{ "%.2f"|format(item.price) }}</p>
                                    <p class="card-text"><small class="text-muted">发布于: {{ moment(item.date_posted).format('ll') if moment else item.date_posted.strftime('%Y-%m-%d') }}</small></p>
                                    {% if item.is_sold %}
                                        <p class="text-success fw-bold">已售出</p>
                                    {% endif %}
                                    <a href="{{ url_for('main.item_detail', item_id=item.id) }}" class="btn btn-primary btn-sm">查看详情</a>
                                    {% if user == current_user and not item.is_sold %}
                                        <a href="{{ url_for('main.edit_item', item_id=item.id) }}" class="btn btn-secondary btn-sm">编辑</a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                <!-- Pagination for user's items -->
                <nav aria-label="User items navigation">
                    <ul class="pagination justify-content-center mt-4">
                        {% if prev_url %}
                            <li class="page-item"><a class="page-link" href="{{ prev_url }}">上一页</a></li>
                        {% else %}
                            <li class="page-item disabled"><span class="page-link">上一页</span></li>
                        {% endif %}
                        {% if next_url %}
                            <li class="page-item"><a class="page-link" href="{{ next_url }}">下一页</a></li>
                        {% else %}
                            <li class="page-item disabled"><span class="page-link">下一页</span></li>
                        {% endif %}
                    </ul>
                </nav>
            {% else %}
                <p>{{ user.username }} 还没有发布任何物品。</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}