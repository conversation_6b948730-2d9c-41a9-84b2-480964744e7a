<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {% if title %}
    <title>{{ title }} - 校园二手市场</title>
    {% else %}
    <title>欢迎来到校园二手市场</title>
    {% endif %}
    <!-- Bootstrap CSS (you can choose a different CSS framework or write custom CSS) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body {
            padding-top: 5rem; /* Adjust if navbar height changes */
            background-color: #f8f9fa;
        }
        .footer {
            background-color: #e9ecef;
            padding: 1rem 0;
            text-align: center;
            margin-top: 2rem;
        }
        .item-card {
            margin-bottom: 1.5rem;
        }
        .item-card img {
            max-height: 200px; /* Limit image height */
            object-fit: cover; /* Ensure image covers the area well */
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-md navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">校园二手市场</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse"
                    aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarCollapse">
                <ul class="navbar-nav me-auto mb-2 mb-md-0">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">首页</a>
                    </li>
                    {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.post_item') }}">发布物品</a>
                        </li>
                    {% endif %}
                    <!-- Add more navigation links here -->
                </ul>
                <ul class="navbar-nav ms-auto mb-2 mb-md-0">
                    {% if current_user.is_anonymous %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">注册</a>
                    </li>
                    {% else %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('main.user', username=current_user.username) }}">我的主页</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.edit_profile') }}">编辑资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">登出</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <main class="container mt-5">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category if category != 'message' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        {% block content %}{% endblock %}
    </main>

    <footer class="footer">
        <div class="container">
            <span class="text-muted">© {{ moment().year() }} 校园二手市场. 保留所有权利.</span>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle (Popper.js and Bootstrap JS) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Moment.js for date formatting (optional, if you use it in templates) -->
    {{ moment.include_moment() if moment else '' }}
</body>
</html>