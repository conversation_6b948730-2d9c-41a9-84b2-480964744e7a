/* Custom styles for the Campus Secondhand Market */

body {
    font-family: 'Arial', sans-serif;
    background-color: #f4f7f6; /* Light grayish background */
    color: #333;
}

.navbar-brand {
    font-weight: bold;
}

.item-card {
    transition: transform .2s ease-in-out, box-shadow .2s ease-in-out;
    border: 1px solid #e0e0e0;
}

.item-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.1);
}

.item-card img {
    border-bottom: 1px solid #eee;
}

.card-title a {
    color: #007bff;
    text-decoration: none;
}

.card-title a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.footer {
    font-size: 0.9em;
    color: #6c757d;
}

/* Profile page specific styles */
.profile-header {
    background-color: #007bff;
    color: white;
    padding: 2rem;
    margin-bottom: 2rem;
    border-radius: .25rem;
}

/* Form styling improvements */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.alert {
    border-radius: .25rem;
}

/* Ensure images in item details are responsive and well-behaved */
.item-detail-img {
    max-width: 100%;
    height: auto;
    border-radius: .25rem;
    margin-bottom: 1rem;
}

/* Style for sold items */
.item-sold-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    border-radius: .25rem; /* Match card's border-radius */
}

.card.item-card .position-relative {
    /* For placing sold overlay if needed directly on card image */
}